/// PumpFun事件定义和解析
/// 
/// 基于参考模块实现的PumpFun程序事件结构

use borsh::{BorshDeserialize, BorshSerialize};
use serde::{Deserialize, Serialize};
use std::io::{self};
use crate::plugins::pump::types::constants::*;
use log::debug;
use serde_with::serde_as;
use solana_program::pubkey::Pubkey;
use chrono::Timelike;

// 添加Pubkey序列化为Base58字符串的辅助模块
mod pubkey_serde {
    use serde::{Deserialize, Deserializer, Serializer};
    use solana_program::pubkey::Pubkey;

    pub fn serialize<S>(pubkey: &Pubkey, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        serializer.serialize_str(&pubkey.to_string())
    }

    pub fn deserialize<'de, D>(deserializer: D) -> Result<Pubkey, D::Error>
    where
        D: Deserializer<'de>,
    {
        let s = String::deserialize(deserializer)?;
        s.parse().map_err(serde::de::Error::custom)
    }
}

/// 创建事件
#[serde_as]
#[derive(Clone, Debug, PartialEq, BorshDeserialize, BorshSerialize, Serialize, Deserialize)]
pub struct CreateEvent {
    /// 代币名称
    pub name: String,
    /// 代币符号
    pub symbol: String,
    /// 代币URI
    pub uri: String,
    /// 代币mint地址
    #[serde(with = "pubkey_serde")]
    pub mint: Pubkey,
    /// 债券曲线地址
    #[serde(with = "pubkey_serde")]
    pub bonding_curve: Pubkey,
    /// 用户地址
    #[serde(with = "pubkey_serde")]
    pub user: Pubkey,
}

/// 创建事件包装器
#[derive(Clone, Debug, PartialEq, Serialize, Deserialize)]
pub struct CreateEventEvent(pub CreateEvent);

impl CreateEventEvent {
    /// 从字节数据反序列化创建事件
    pub fn deserialize(buf: &mut &[u8]) -> std::io::Result<Self> {
        let maybe_discm = <[u8; 8] as BorshDeserialize>::deserialize(buf)?;
        if maybe_discm != CREATE_EVENT_EVENT_DISCM {
            return Err(std::io::Error::new(
                std::io::ErrorKind::Other,
                format!(
                    "创建事件判别符不匹配. 期望: {:?}. 实际: {:?}",
                    CREATE_EVENT_EVENT_DISCM, maybe_discm
                ),
            ));
        }
        Ok(Self(<CreateEvent as BorshDeserialize>::deserialize(buf)?))
    }
}

/// 交易事件（核心CPI事件）
#[serde_as]
#[derive(Clone, Debug, PartialEq, BorshDeserialize, BorshSerialize, Serialize, Deserialize)]
pub struct TradeEvent {
    /// 代币mint地址
    #[serde(with = "pubkey_serde")]
    pub mint: Pubkey,
    /// SOL金额（lamports）
    pub sol_amount: u64,
    /// 代币金额
    pub token_amount: u64,
    /// 是否为购买操作
    pub is_buy: bool,
    /// 用户地址
    #[serde(with = "pubkey_serde")]
    pub user: Pubkey,
    /// 时间戳
    pub timestamp: i64,
    /// 虚拟SOL储备
    pub virtual_sol_reserves: u64,
    /// 虚拟代币储备
    pub virtual_token_reserves: u64,
    /// 真实SOL储备
    pub real_sol_reserves: u64,
    /// 真实代币储备
    pub real_token_reserves: u64,
    /// 费用接收者
    #[serde(with = "pubkey_serde")]
    pub fee_recipient: Pubkey,
    /// 费用基点
    pub fee_basis_points: u64,
    /// 费用金额
    pub fee: u64,
    /// 创作者地址
    #[serde(with = "pubkey_serde")]
    pub creator: Pubkey,
    /// 创作者费用基点
    pub creator_fee_basis_points: u64,
    /// 创作者费用金额
    pub creator_fee: u64,
}

/// 交易事件包装器
#[derive(Clone, Debug, PartialEq, Serialize, Deserialize)]
pub struct TradeEventEvent(pub TradeEvent);

/// 包含签名信息的完整交易事件
#[derive(Clone, Debug, PartialEq, Serialize, Deserialize)]
pub struct TradeEventWithSignature {
    /// 交易事件数据
    #[serde(flatten)]
    pub event: TradeEvent,
    /// 交易签名
    pub signature: String,
}

/// 包含计算字段的扩展交易事件
#[derive(Clone, Debug, PartialEq, Serialize, Deserialize)]
pub struct TradeEventExtended {
    /// 原始交易事件数据
    #[serde(flatten)]
    pub event: TradeEvent,
    /// 计算得出的创作者金库地址
    #[serde(with = "pubkey_serde")]
    pub creator_vault: Pubkey,
    /// 交易签名（可选）
    pub signature: Option<String>,
    /// 计算得出的当前价格 (SOL/Token)
    pub current_price: Option<f64>,
    /// 网络延迟（微秒）
    pub network_delay_micros: Option<u64>,
}

impl TradeEventExtended {
    /// 将交易事件转换为指定的文本格式
    pub fn to_text_format(&self) -> String {
        // 交易类型
        let type_str = if self.event.is_buy { "Buy" } else { "Sell" };
        
        // SOL金额格式化为小数点后6位
        let sol_amount = self.event.sol_amount as f64 / 1_000_000_000.0; // lamports到SOL的转换
        
        // 价格格式化为科学计数法
        let price_str = match self.current_price {
            Some(p) => format!("{:e}", p),
            None => "未知".to_string(),
        };
        
        // 使用东八区时间格式并确保有7位小数精度
        let utc_time = chrono::Utc::now();
        let east_eight = chrono::FixedOffset::east_opt(8 * 3600).unwrap();
        let local_time = utc_time.with_timezone(&east_eight);
        let time_str = format!("{}.{:07}+08:00",
            local_time.format("%Y-%m-%dT%H:%M:%S"),
            local_time.nanosecond() / 100); // 7位小数精度

        // 格式化网络延迟
        let delay_str = match self.network_delay_micros {
            Some(delay_micros) => {
                if delay_micros >= 1000 {
                    format!("{:.3}ms", delay_micros as f64 / 1000.0)
                } else {
                    format!("{}μs", delay_micros)
                }
            },
            None => "未知".to_string(),
        };
        
        // 创建格式化的文本
        format!(
            "TYPE: {}\n\
            SIGNATURE: {}\n\
            签名者地址: {}\n\
            MINT: {}\n\
            TOKEN AMOUNT: {}\n\
            SOL COST: {:.6} SOL\n\
            当前价格: {}\n\
            创作者金库地址: {}\n\
            VIRTUAL TOKEN RESERVES: {}\n\
            VIRTUAL SOL RESERVES: {}\n\
            REAL TOKEN RESERVES: {}\n\
            REAL SOL RESERVES: {}\n\
            TOKEN TOTAL SUPPLY: 未知\n\
            COMPLETE: true\n\
            TIME: {}\n\
            网络延迟: {}",
            type_str,
            self.signature.clone().unwrap_or_else(|| "未知".to_string()),
            self.event.user.to_string(),
            self.event.mint.to_string(),
            self.event.token_amount,
            sol_amount,
            price_str,
            self.creator_vault.to_string(),
            self.event.virtual_token_reserves,
            self.event.virtual_sol_reserves,
            self.event.real_token_reserves,
            self.event.real_sol_reserves,
            time_str,
            delay_str
        )
    }
}

impl TradeEventEvent {
    /// 从字节数据反序列化交易事件
    pub fn deserialize(buf: &mut &[u8]) -> std::io::Result<Self> {
        let maybe_discm = <[u8; 8] as BorshDeserialize>::deserialize(buf)?;
        if maybe_discm != TRADE_EVENT_EVENT_DISCM {
            return Err(std::io::Error::new(
                std::io::ErrorKind::Other,
                format!(
                    "交易事件判别符不匹配. 期望: {:?}. 实际: {:?}",
                    TRADE_EVENT_EVENT_DISCM, maybe_discm
                ),
            ));
        }
        Ok(Self(<TradeEvent as BorshDeserialize>::deserialize(buf)?))
    }

    /// 从base64编码的数据解析交易事件
    pub fn from_base64(data: &str) -> Result<Self, Box<dyn std::error::Error>> {
        use base64::{engine::general_purpose::STANDARD, Engine as _};
        
        let decoded_data = match STANDARD.decode(data) {
            Ok(data) => data,
            Err(err) => {
                return Err(format!("Base64解码失败: {}", err).into());
            }
        };
        
        if decoded_data.len() < 8 {
            return Err(format!("数据长度不足，长度: {}", decoded_data.len()).into());
        }

        // 检查判别符
        let discm = &decoded_data[..8];
        
        if discm != TRADE_EVENT_EVENT_DISCM {
            return Err(format!(
                "交易事件判别符不匹配. 期望: {:?}, 实际: {:?}", 
                TRADE_EVENT_EVENT_DISCM, discm
            ).into());
        }
        
        // 跳过判别符，解析事件数据
        let event_data = &decoded_data[8..];
        match TradeEvent::try_from_slice(event_data) {
            Ok(trade_event) => {
                Ok(Self(trade_event))
            },
            Err(err) => {
                Err(format!("事件数据反序列化失败: {}", err).into())
            }
        }
    }
}

/// 完成事件
#[serde_as]
#[derive(Clone, Debug, PartialEq, BorshDeserialize, BorshSerialize, Serialize, Deserialize)]
pub struct CompleteEvent {
    /// 用户地址
    #[serde(with = "pubkey_serde")]
    pub user: Pubkey,
    /// 代币mint地址
    #[serde(with = "pubkey_serde")]
    pub mint: Pubkey,
    /// 债券曲线地址
    #[serde(with = "pubkey_serde")]
    pub bonding_curve: Pubkey,
    /// 时间戳
    pub timestamp: i64,
}

/// 完成事件包装器
#[derive(Clone, Debug, PartialEq, Serialize, Deserialize)]
pub struct CompleteEventEvent(pub CompleteEvent);

impl CompleteEventEvent {
    /// 从字节数据反序列化完成事件
    pub fn deserialize(buf: &mut &[u8]) -> std::io::Result<Self> {
        let maybe_discm = <[u8; 8] as BorshDeserialize>::deserialize(buf)?;
        if maybe_discm != COMPLETE_EVENT_EVENT_DISCM {
            return Err(std::io::Error::new(
                std::io::ErrorKind::Other,
                format!(
                    "完成事件判别符不匹配. 期望: {:?}. 实际: {:?}",
                    COMPLETE_EVENT_EVENT_DISCM, maybe_discm
                ),
            ));
        }
        Ok(Self(<CompleteEvent as BorshDeserialize>::deserialize(buf)?))
    }
}

/// 通用事件解析器
pub struct EventParser;

impl EventParser {
    /// 从日志消息中解析事件
    pub fn parse_from_log(log_message: &str) -> Option<TradeEventEvent> {
        // 标准化日志消息，避免因特殊字符或空格导致解析失败
        let trimmed_log = log_message.trim();
        
        if !trimmed_log.starts_with(PROGRAM_DATA_PREFIX) {
            return None;
        }

        let data_part = trimmed_log.trim_start_matches(PROGRAM_DATA_PREFIX).trim();
        
        // 尝试解析TradeEvent
        match TradeEventEvent::from_base64(data_part) {
            Ok(trade_event) => {
                debug!("成功解析TradeEvent事件");
                Some(trade_event)
            }
            Err(err) => {
                debug!("解析TradeEvent事件失败: {}", err);
                None
            }
        }
    }

    /// 检查数据是否为交易事件
    pub fn is_trade_event(data: &[u8]) -> bool {
        data.len() >= 8 && data[..8] == TRADE_EVENT_EVENT_DISCM
    }

    /// 检查数据是否为创建事件
    pub fn is_create_event(data: &[u8]) -> bool {
        data.len() >= 8 && data[..8] == CREATE_EVENT_EVENT_DISCM
    }

    /// 检查数据是否为完成事件
    pub fn is_complete_event(data: &[u8]) -> bool {
        data.len() >= 8 && data[..8] == COMPLETE_EVENT_EVENT_DISCM
    }
}
