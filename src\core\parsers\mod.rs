/// 通用解析器接口模块
/// 
/// 定义了所有交易解析器必须实现的接口

use std::sync::Arc;
use anyhow::Result;
use async_trait::async_trait;
use yellowstone_grpc_proto::geyser::SubscribeUpdateTransaction;
use bs58;

/// 通用原始交易数据结构
#[derive(Clone)]
pub struct RawTransaction {
    pub signature: String,
    pub account_keys: Vec<Vec<u8>>,
    pub transaction: SubscribeUpdateTransaction,
    pub logs: Box<[String]>,
    pub timestamp: u64,
}

impl RawTransaction {
    // 添加从SubscribeUpdateTransaction转换的方法
    pub fn from_update(tx_update: SubscribeUpdateTransaction) -> Option<Self> {
        let transaction = tx_update.transaction.as_ref()?;
        let signature = bs58::encode(&transaction.signature).into_string();
        
        // 提取账户密钥和recent_blockhash
        let account_keys = if let Some(raw_transaction) = &transaction.transaction {
            if let Some(raw_message) = &raw_transaction.message {
                // 尝试获取recent_blockhash
                if let Some(recent_blockhash) = &raw_message.recent_blockhash {
                    println!("找到recent_blockhash: {:?}", bs58::encode(recent_blockhash).into_string());
                }
                raw_message.account_keys.clone()
            } else {
                Vec::new()
            }
        } else {
            Vec::new()
        };
        
        // 提取日志
        let logs = if let Some(meta) = &transaction.meta {
            meta.log_messages.clone().into_boxed_slice()
        } else {
            Box::new([])
        };
        
        // 获取当前时间戳作为处理时间
        let timestamp = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();
        
        Some(Self {
            signature,
            account_keys,
            transaction: tx_update,
            logs,
            timestamp,
        })
    }
}

/// 通用交易解析器接口
#[async_trait]
pub trait TransactionParser: Send + Sync {
    /// 获取解析器名称
    fn name(&self) -> &str;
    
    /// 检查是否可以处理指定的交易
    fn can_handle(&self, account_keys: &[Vec<u8>]) -> bool;
    
    /// 解析交易数据
    async fn parse(&self, transaction: Arc<RawTransaction>) -> Result<()>;
}

/// 解析器工厂，用于创建和管理解析器实例
pub struct ParserFactory {
    parsers: Vec<Box<dyn TransactionParser>>,
}

impl ParserFactory {
    /// 创建新的解析器工厂
    pub fn new() -> Self {
        Self {
            parsers: Vec::new(),
        }
    }
    
    /// 添加解析器
    pub fn add_parser<P: TransactionParser + 'static>(&mut self, parser: P) {
        self.parsers.push(Box::new(parser));
    }
    
    /// 获取可以处理指定交易的解析器
    pub fn get_parser_for_transaction(&self, account_keys: &[Vec<u8>]) -> Option<&dyn TransactionParser> {
        for parser in &self.parsers {
            if parser.can_handle(account_keys) {
                return Some(parser.as_ref());
            }
        }
        None
    }

    /// 分发交易到对应的解析器
    pub async fn dispatch(&self, transaction: Arc<RawTransaction>) -> Result<()> {
        if let Some(parser) = self.get_parser_for_transaction(&transaction.account_keys) {
            return parser.parse(transaction).await;
        }
        Ok(())
    }
}

impl Default for ParserFactory {
    fn default() -> Self {
        Self::new()
    }
} 